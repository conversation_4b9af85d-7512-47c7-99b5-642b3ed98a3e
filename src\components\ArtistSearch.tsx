import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const ArtistSearch = () => {
  return (
    <section className="py-20 px-6 md:px-12 bg-background">
      <div className="max-w-4xl mx-auto text-center">
        <h2 className="text-3xl md:text-4xl font-semibold text-foreground mb-6">
          Can't find who you're looking for?
        </h2>
        <p className="text-lg text-muted-foreground mb-10 max-w-2xl mx-auto">
          Our network consists of an enormous amount of artists spanning every genre, style, and performance type. 
          From emerging talents to established stars, we have connections with artists worldwide who can bring 
          your vision to life and make your event unforgettable.
        </p>
        <div className="flex justify-center pt-4">
          <Link to="/contact">
            <Button className="group inline-flex items-center gap-2 bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 rounded-full">
              <span>Work with us</span>
              <svg
                className="w-4 h-4 transition-transform duration-200 group-hover:translate-x-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ArtistSearch;