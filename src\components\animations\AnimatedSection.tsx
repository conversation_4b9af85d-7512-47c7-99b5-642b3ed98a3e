import React, { useRef } from 'react';
import { motion, useInView, Variants } from 'framer-motion';
import { fadeInUp, getReducedMotionVariants } from '@/lib/animations';

interface AnimatedSectionProps {
  children: React.ReactNode;
  className?: string;
  variants?: Variants;
  delay?: number;
  threshold?: number;
  once?: boolean;
}

const AnimatedSection: React.FC<AnimatedSectionProps> = ({
  children,
  className = '',
  variants = fadeInUp,
  delay = 0,
  threshold = 0.1,
  once = true,
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { 
    threshold, 
    once,
    margin: "-50px 0px -50px 0px"
  });

  const animationVariants = getReducedMotionVariants(variants);

  // Add delay to the variants if specified
  const delayedVariants = delay > 0 ? {
    ...animationVariants,
    visible: {
      ...animationVariants.visible,
      transition: {
        ...animationVariants.visible.transition,
        delay,
      },
    },
  } : animationVariants;

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={delayedVariants}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default AnimatedSection;
