import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import CTAStrip from '@/components/CTAStrip';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Users, Globe, TrendingUp, Star, Award } from 'lucide-react';

const Agency = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  const services = [
    {
      title: "Artist management",
      description: "Enhance your DJ career with tailored strategies, industry connections, and global exposure.",
      action: "Start Your Journey",
      icon: <Users className="h-8 w-8 text-primary" />,
    },
    {
      title: "Book an artist",
      description: "Book your next event with the help of our extensive network of DJs and producers.",
      action: "View artists",
      icon: <Globe className="h-8 w-8 text-primary" />,
    },
    {
      title: "Custom",
      description: "Are you looking for something specific? We also offer photo, video, and equipment services.",
      action: "Let's talk",
      icon: <TrendingUp className="h-8 w-8 text-primary" />,
    }
  ];

  const clientLogos = [
    { name: "Absolut", src: "/clients/absolut.png" },
    { name: "Brooklyn", src: "/clients/brooklyn.png" },
    { name: "Brugge", src: "/clients/brugge.png" },
    { name: "Cercle", src: "/clients/cercle.png" },
    { name: "Jet Import", src: "/clients/jetimport.png" },
    { name: "Knokke Heist", src: "/clients/kh2.png" },
    { name: "Pernod Ricard", src: "/clients/pernodricard.png" },
    { name: "Toms Tourney", src: "/clients/tt.png" },
    { name: "VEK", src: "/clients/vek.png" }
  ];

  const caseStudies = [
    {
      title: "Global Festival Circuit",
      description: "Secured headline slots at 15+ major festivals worldwide, transforming an emerging artist into a global headliner with over 2M festival attendees.",
      image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop&crop=center",
      metric: "15+ Festivals",
      icon: <Star className="h-6 w-6 text-primary" />
    },
    {
      title: "Digital Transformation",
      description: "300% increase in streaming numbers through strategic digital campaign, reaching 10M+ monthly listeners across all platforms.",
      image: "https://images.unsplash.com/photo-1611532736597-de2d4265fba3?w=400&h=300&fit=crop&crop=center",
      metric: "300% Growth",
      icon: <TrendingUp className="h-6 w-6 text-primary" />
    },
    {
      title: "Brand Partnership",
      description: "Landmark collaboration with luxury fashion house for exclusive collection, generating €2M+ in revenue and global media coverage.",
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop&crop=center",
      metric: "€2M+ Revenue",
      icon: <Award className="h-6 w-6 text-primary" />
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 cosmic-grid opacity-30"></div>
      <div className="absolute top-[450px] left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full">
        <div className="w-full h-full opacity-10 bg-primary blur-[120px]"></div>
      </div>
      
      <div className="relative z-10 flex flex-col min-h-screen">
        <Header />
        <main className="flex-1">
          {/* Hero Section */}
          <section className="relative w-full py-20 md:py-24 mb-10 px-6 md:px-12 flex flex-col items-center justify-center">
            <div className={`relative z-10 max-w-5xl text-center space-y-8 transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
              <div className="flex justify-center">
                <span className="border border-muted inline-flex items-center gap-2 px-3 py-1 text-xs font-medium rounded-full text-primary transition-colors cursor-pointer">
                  Leading Artist Management
                </span>
              </div>
              
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-semibold text-balance text-foreground">
                Shaping the future of <span className="text-primary">artist management</span>
              </h1>
              
              <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                We elevate exceptional artists to global recognition through strategic management, innovative partnerships, and unparalleled industry expertise.
              </p>

              
            </div>
          </section>

          {/* Stats Section
          <section className="py-16 px-6 md:px-12 border-y border-border bg-muted/30">
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div className="space-y-2">
                  <div className="text-3xl md:text-4xl font-bold text-primary">50+</div>
                  <div className="text-sm text-muted-foreground">Artists Managed</div>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl md:text-4xl font-bold text-primary">200+</div>
                  <div className="text-sm text-muted-foreground">Events Annually</div>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl md:text-4xl font-bold text-primary">10M+</div>
                  <div className="text-sm text-muted-foreground">Streams Generated</div>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl md:text-4xl font-bold text-primary">12</div>
                  <div className="text-sm text-muted-foreground">Years Experience</div>
                </div>
              </div>
            </div>
          </section> */}

        {/* About Section */}
        <section className="py-16 px-6 md:px-12">
          <div className="max-w-4xl mx-auto text-center space-y-6">
            <h2 className="text-3xl md:text-4xl font-semibold">Our mission</h2>
            <p className="text-lg text-muted-foreground leading-relaxed text-balance">
              For over a decade, we've been at the forefront of electronic music, discovering and nurturing talent that defines the sound of tomorrow. Our boutique approach ensures every artist receives personalized attention while leveraging our extensive global network to create extraordinary opportunities.
            </p>
            <div className="flex justify-center pt-4">
              <Link to="/contact">
                <Button className="group inline-flex items-center gap-2 bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 rounded-full">
                  <span>Work with us</span>
                  <svg
                    className="w-4 h-4 transition-transform duration-200 group-hover:translate-x-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20 px-6 md:px-12">
          <div className="max-w-6xl mx-auto">
           

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <Card key={index} className="border-border bg-card hover:border-primary/20 hover:shadow-lg transition-all duration-300 group rounded-xl">
                  <CardContent className="p-8 space-y-6 text-left">
                    <div className="flex items-center justify-between">
                      <div className="p-3 rounded-xl bg-primary/10 group-hover:bg-primary/20 transition-colors duration-300">
                        {service.icon}
                      </div>
                    </div>
                    <h3 className="text-2xl font-semibold">{service.title}</h3>
                    <p className="text-muted-foreground leading-relaxed">{service.description}</p>
                    <Link to="/contact" >
                      <button className="mt-4 font-semibold group/btn inline-flex items-center gap-2 text-lg text-foreground hover:text-primary transition-all duration-200 cursor-pointer">
                        <span>{service.action}</span>
                        <svg
                          className="w-5 h-5 transition-transform duration-200 group-hover/btn:translate-x-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Client Logos - Auto Scrolling Strip */}
        <section className="py-12 overflow-hidden border-y border-border client-logos-bg">
          <div className="text-center mb-12">
            <p className="text-white/70">Trusted by the best</p>
          </div>
          <div className="relative">
            {/* Auto-scrolling container */}
            <div className="flex animate-scroll-left ">
              {/* First set of logos */}
              {clientLogos.map((client, index) => (
                <div key={`first-${index}`} className="flex-shrink-0 mx-8">
                  <div className="w-40 h-30 flex items-center justify-center p-4 hover:scale-105 transition-all duration-300">
                    <img
                      src={client.src}
                      alt={client.name}
                      className="max-w-full max-h-full object-contain opacity-90 hover:opacity-100 transition-opacity duration-300 filter grayscale hover:grayscale-0"
                    />
                  </div>
                </div>
              ))}
              {/* Duplicate set for seamless loop */}
              {clientLogos.map((client, index) => (
                <div key={`second-${index}`} className="flex-shrink-0 mx-8">
                  <div className="w-40 h-30 flex items-center justify-center p-4 hover:scale-105 transition-all duration-300">
                    <img
                      src={client.src}
                      alt={client.name}
                      className="max-w-full max-h-full object-contain opacity-60 hover:opacity-100 transition-opacity duration-300 filter grayscale hover:grayscale-0"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Case Studies */}
        <section className="py-20 px-6 md:px-12 bg-background">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-semibold mb-4">Case studies</h2>
              <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                Real results from our strategic partnerships and innovative campaigns
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {caseStudies.map((study, index) => (
                <Card key={index} className="border-border bg-card hover:border-primary/20 hover:shadow-lg transition-all duration-300 group overflow-hidden rounded-xl">
                  <div className="aspect-[4/3] overflow-hidden">
                    <img
                      src={study.image}
                      alt={study.title}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                    />
                  </div>
                  <CardContent className="p-6 space-y-4 text-left">
                    <div className="flex items-center justify-between">
                      <div className="p-2 rounded-xl bg-primary/10">
                        {study.icon}
                      </div>
                      <span className="text-sm font-semibold text-primary">{study.metric}</span>
                    </div>
                    <h3 className="text-xl font-semibold">{study.title}</h3>
                    <p className="text-muted-foreground leading-relaxed text-sm">{study.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
        </main>
        <CTAStrip />
        <Footer />
      </div>
    </div>
  );
};

export default Agency;