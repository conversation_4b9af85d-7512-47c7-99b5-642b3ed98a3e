import React from 'react';
import Header from '@/components/Header';
import CTAStrip from '@/components/CTAStrip';
import Footer from '@/components/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { hero<PERSON>ontainer, heroTitle, heroSubtitle, staggerContainer, staggerItem, getReducedMotionVariants } from '@/lib/animations';
import { AnimatedSection, StaggeredGrid, PageTransition } from '@/components/animations';

const About = () => {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Founder & CEO",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=400&h=400&fit=crop&crop=face",
      email: "<EMAIL>",
      phone: "+****************"
    },
    {
      name: "<PERSON>",
      role: "Head of Booking",
      image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901?w=400&h=400&fit=crop&crop=face",
      email: "<EMAIL>",
      phone: "+****************"
    },
    {
      name: "<PERSON> <PERSON><PERSON>",
      role: "Artist Development Director",
      image: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?w=400&h=400&fit=crop&crop=face",
      email: "<EMAIL>",
      phone: "+****************"
    },
    {
      name: "James Kim",
      role: "Digital Strategy Lead",
      image: "https://images.unsplash.com/photo-1501286353178-1ec881214838?w=400&h=400&fit=crop&crop=face",
      email: "<EMAIL>",
      phone: "+****************"
    },
    {
      name: "Luna Petrov",
      role: "Creative Director",
      image: "https://images.unsplash.com/photo-1518005020951-eccb494ad742?w=400&h=400&fit=crop&crop=face",
      email: "<EMAIL>",
      phone: "+****************"
    },
    {
      name: "David Thompson",
      role: "Tour Manager",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=400&h=400&fit=crop&crop=face",
      email: "<EMAIL>",
      phone: "+****************"
    }
  ];

  return (
    <PageTransition className="min-h-screen flex flex-col bg-background text-foreground relative overflow-hidden">
      {/* Cosmic particle effect (background dots) */}
      <div className="absolute inset-0 cosmic-grid opacity-30"></div>

      {/* Gradient glow effect */}
      <div className="absolute top-[450px] left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full">
        <div className="w-full h-full opacity-10 bg-primary blur-[120px]"></div>
      </div>

      <div className="relative z-10 flex flex-col min-h-screen">
        <Header />
        <main className="flex-1">
          {/* Hero Section */}
          <AnimatedSection className="relative w-full py-20 md:py-28 px-6 md:px-12 flex flex-col items-center justify-center">
            <motion.div
              className="relative z-10 max-w-5xl text-center space-y-8"
              variants={getReducedMotionVariants(heroContainer)}
              initial="hidden"
              animate="visible"
            >
              <motion.div
                className="flex justify-center"
                variants={getReducedMotionVariants(staggerItem)}
              >
                <span className="inline-flex items-center gap-2 px-3 py-1 text-xs font-medium rounded-full bg-muted text-primary">
                  <span className="flex h-2 w-2 rounded-full bg-primary"></span>
                  Our Story
                </span>
              </motion.div>

              <motion.h1
                className="text-4xl md:text-6xl lg:text-7xl font-semibold tracking-tighter text-balance text-foreground"
                variants={getReducedMotionVariants(heroTitle)}
              >
                Meet the <span className="text-primary">Team</span>
              </motion.h1>

              <motion.p
                className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
                variants={getReducedMotionVariants(heroSubtitle)}
              >
                Passionate music industry professionals dedicated to elevating electronic music artists worldwide.
              </motion.p>
            </motion.div>
          </AnimatedSection>

        {/* Mission Section */}
        <AnimatedSection className="py-16 px-6 md:px-12">
          <motion.div
            className="max-w-4xl mx-auto text-center space-y-8"
            variants={getReducedMotionVariants(staggerContainer)}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-50px" }}
          >
            <motion.h2
              className="text-3xl md:text-4xl font-semibold"
              variants={getReducedMotionVariants(staggerItem)}
            >
              Our Mission
            </motion.h2>
            <motion.p
              className="text-lg text-muted-foreground leading-relaxed"
              variants={getReducedMotionVariants(staggerItem)}
            >
              Founded in 2014, our agency emerged from a shared vision to bridge the gap between exceptional electronic music talent and the global stage. We believe that great music transcends boundaries, and our mission is to provide artists with the tools, connections, and strategic guidance they need to reach audiences worldwide.
            </motion.p>
          </motion.div>
        </AnimatedSection>

        {/* Team Section */}
        <AnimatedSection className="py-16 px-6 md:px-12">
          <div className="max-w-6xl mx-auto">
            <StaggeredGrid className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {teamMembers.map((member, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.02, y: -5 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Card className="border-border bg-card hover:border-primary/20 transition-all duration-300 overflow-hidden h-full">
                    <div className="aspect-square overflow-hidden">
                      <img
                        src={member.image}
                        alt={member.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <CardContent className="p-8 space-y-4 text-left">
                      <h3 className="text-2xl font-semibold">{member.name}</h3>
                      <p className="text-primary font-medium">{member.role}</p>
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <p>{member.email}</p>
                        <p>{member.phone}</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </StaggeredGrid>
          </div>
        </AnimatedSection>
      </main>
      <CTAStrip />
      <Footer />
      </div>
    </PageTransition>
  );
};

export default About;